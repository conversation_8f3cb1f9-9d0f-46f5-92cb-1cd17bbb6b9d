import React from 'react';
import GreenIconSvg from '@src/assets/svg-component/green-icon.svg';
import NodeInspectionSvg from '@src/assets/svg-component/node-inspection.svg';
import { RiskLevelEnum } from '@src/constants';
import s from './index.module.scss';

interface IShapeNumberBarProps {
  riskInfoItem: any
}
/**
 * 图元角标组件
 * @returns
 */
export default function ShapeRiskBar(props: IShapeNumberBarProps): React.ReactElement {
  const {
    riskInfoItem,
  } = props;

  if (archTaskStatus && !nodeTaskStatus?.IsFinish) {
    // 当架构图完成巡检节点没有完成巡检时节点显示loading状态
    return (
      <NodeInspectionSvg
        // @ts-ignore
        className="node-inspection-ing"
      />
    );
  }

  if (!hasTask) {
    return <></>;
  }

  if (riskInfoItem?.RiskType === RiskLevelEnum.HIGH) {
    return <GreenIconSvg />;
  }

  return (
    <div className={classNames(
      s['shape-number-bar'],
      {
        [s['shape-number-bar-nobg']]: !(highRiskCount > 0 && mediumRiskCount > 0)
        || riskFilterType === RiskFilterTypeEnum.HIGH_RISK_ONLY
        || riskFilterType === RiskFilterTypeEnum.MID_RISK_ONLY,
      },
    )}
    >
      {
        !!highRiskCount
        && (riskFilterType === RiskFilterTypeEnum.HIGH_RISK_ONLY || riskFilterType === RiskFilterTypeEnum.ALL_RISK)
        && (
          <div
            style={{
              display: 'flex',
              minWidth: '28px',
              height: '28px',
              boxSizing: 'border-box',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '5px 9px',
              borderRadius: '14px',
              marginRight: '4px',
              background: '#e54545',
              color: '#fff',
              fontWeight: '600',
            }}
          >
            {highRiskCount > 99 ? '99+' : highRiskCount}
          </div>
        )
      }
      {
        !!mediumRiskCount
        && (riskFilterType === RiskFilterTypeEnum.MID_RISK_ONLY || riskFilterType === RiskFilterTypeEnum.ALL_RISK)
        && (
          <div
            style={{
              display: 'flex',
              minWidth: '28px',
              height: '28px',
              boxSizing: 'border-box',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '5px 9px',
              borderRadius: '14px',
              background: '#ff7200',
              color: '#fff',
              fontWeight: '600',
            }}
          >
            {mediumRiskCount > 99 ? '99+' : mediumRiskCount}
          </div>
        )
      }
    </div>
  );
}
